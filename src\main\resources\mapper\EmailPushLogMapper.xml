<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zqn.email.mapper.EmailPushLogMapper">

    <resultMap id="ResultMap" type="com.zqn.email.entity.EmailPushLog">
        <result property="id" column="id" jdbcType="NUMERIC"/>
        <result property="batchId" column="batch_id" jdbcType="VARCHAR"/>
        <result property="emailType" column="email_type" jdbcType="VARCHAR"/>
        <result property="subject" column="subject" jdbcType="VARCHAR"/>
        <result property="content" column="content" jdbcType="CLOB"/>
        <result property="fromEmail" column="from_email" jdbcType="VARCHAR"/>
        <result property="toEmail" column="to_email" jdbcType="VARCHAR"/>
        <result property="ccEmail" column="cc_email" jdbcType="VARCHAR"/>
        <result property="bccEmail" column="bcc_email" jdbcType="VARCHAR"/>
        <result property="userNo" column="user_no" jdbcType="VARCHAR"/>
        <result property="deptName" column="dept_name" jdbcType="VARCHAR"/>
        <result property="businessId" column="business_id" jdbcType="VARCHAR"/>
        <result property="businessType" column="business_type" jdbcType="VARCHAR"/>
        <result property="sendStatus" column="send_status" jdbcType="VARCHAR"/>
        <result property="sendTime" column="send_time" jdbcType="DATE"/>
        <result property="retryCount" column="retry_count" jdbcType="NUMERIC"/>
        <result property="maxRetry" column="max_retry" jdbcType="NUMERIC"/>
        <result property="nextRetryTime" column="next_retry_time" jdbcType="DATE"/>
        <result property="errorCode" column="error_code" jdbcType="VARCHAR"/>
        <result property="errorMessage" column="error_message" jdbcType="VARCHAR"/>
        <result property="attachmentInfo" column="attachment_info" jdbcType="CLOB"/>
        <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
        <result property="createDate" column="create_date" jdbcType="DATE"/>
        <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
        <result property="updateDate" column="update_date" jdbcType="DATE"/>
    </resultMap>

    <!-- 插入邮件推送日志 -->
    <insert id="insert" parameterType="com.zqn.email.entity.EmailPushLog">
        INSERT INTO pcc_email_push_log (
            id, batch_id, email_type, subject, content, from_email, to_email, cc_email, bcc_email,
            user_no, dept_name, business_id, business_type, send_status, send_time, retry_count,
            max_retry, next_retry_time, error_code, error_message, attachment_info,
            create_user, create_date, update_user, update_date
        ) VALUES (
            seq_pcc_email_push_log.nextval, #{batchId}, #{emailType}, #{subject}, #{content}, 
            #{fromEmail}, #{toEmail}, #{ccEmail}, #{bccEmail}, #{userNo}, #{deptName}, 
            #{businessId}, #{businessType}, #{sendStatus}, #{sendTime}, #{retryCount},
            #{maxRetry}, #{nextRetryTime}, #{errorCode}, #{errorMessage}, #{attachmentInfo},
            #{createUser}, #{createDate}, #{updateUser}, #{updateDate}
        )
    </insert>

    <!-- 批量插入邮件推送日志 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO pcc_email_push_log (
            id, batch_id, email_type, subject, content, from_email, to_email, cc_email, bcc_email,
            user_no, dept_name, business_id, business_type, send_status, send_time, retry_count,
            max_retry, next_retry_time, error_code, error_message, attachment_info,
            create_user, create_date, update_user, update_date
        )
        <foreach collection="logs" item="item" separator="UNION ALL">
            SELECT seq_pcc_email_push_log.nextval, #{item.batchId}, #{item.emailType}, #{item.subject}, #{item.content}, 
                   #{item.fromEmail}, #{item.toEmail}, #{item.ccEmail}, #{item.bccEmail}, #{item.userNo}, #{item.deptName}, 
                   #{item.businessId}, #{item.businessType}, #{item.sendStatus}, #{item.sendTime}, #{item.retryCount},
                   #{item.maxRetry}, #{item.nextRetryTime}, #{item.errorCode}, #{item.errorMessage}, #{item.attachmentInfo},
                   #{item.createUser}, #{item.createDate}, #{item.updateUser}, #{item.updateDate}
            FROM dual
        </foreach>
    </insert>

    <!-- 根据ID更新邮件推送日志 -->
    <update id="updateById" parameterType="com.zqn.email.entity.EmailPushLog">
        UPDATE pcc_email_push_log
        SET send_status = #{sendStatus},
            send_time = #{sendTime},
            retry_count = #{retryCount},
            next_retry_time = #{nextRetryTime},
            error_code = #{errorCode},
            error_message = #{errorMessage},
            update_user = #{updateUser},
            update_date = #{updateDate}
        WHERE id = #{id}
    </update>

    <!-- 根据ID查询邮件推送日志 -->
    <select id="selectById" resultMap="ResultMap">
        SELECT id, batch_id, email_type, subject, content, from_email, to_email, cc_email, bcc_email,
               user_no, dept_name, business_id, business_type, send_status, send_time, retry_count,
               max_retry, next_retry_time, error_code, error_message, attachment_info,
               create_user, create_date, update_user, update_date
        FROM pcc_email_push_log
        WHERE id = #{id}
    </select>

    <!-- 根据批次ID查询邮件推送日志列表 -->
    <select id="selectByBatchId" resultMap="ResultMap">
        SELECT id, batch_id, email_type, subject, content, from_email, to_email, cc_email, bcc_email,
               user_no, dept_name, business_id, business_type, send_status, send_time, retry_count,
               max_retry, next_retry_time, error_code, error_message, attachment_info,
               create_user, create_date, update_user, update_date
        FROM pcc_email_push_log
        WHERE batch_id = #{batchId}
        ORDER BY create_date DESC
    </select>

    <!-- 根据业务ID查询邮件推送日志列表 -->
    <select id="selectByBusinessId" resultMap="ResultMap">
        SELECT id, batch_id, email_type, subject, content, from_email, to_email, cc_email, bcc_email,
               user_no, dept_name, business_id, business_type, send_status, send_time, retry_count,
               max_retry, next_retry_time, error_code, error_message, attachment_info,
               create_user, create_date, update_user, update_date
        FROM pcc_email_push_log
        WHERE business_id = #{businessId}
        ORDER BY create_date DESC
    </select>

    <!-- 根据发送状态查询邮件推送日志列表 -->
    <select id="selectBySendStatus" resultMap="ResultMap">
        SELECT id, batch_id, email_type, subject, content, from_email, to_email, cc_email, bcc_email,
               user_no, dept_name, business_id, business_type, send_status, send_time, retry_count,
               max_retry, next_retry_time, error_code, error_message, attachment_info,
               create_user, create_date, update_user, update_date
        FROM pcc_email_push_log
        WHERE send_status = #{sendStatus}
        ORDER BY create_date DESC
    </select>

    <!-- 查询需要重试的邮件日志 -->
    <select id="selectRetryLogs" resultMap="ResultMap">
        SELECT id, batch_id, email_type, subject, content, from_email, to_email, cc_email, bcc_email,
               user_no, dept_name, business_id, business_type, send_status, send_time, retry_count,
               max_retry, next_retry_time, error_code, error_message, attachment_info,
               create_user, create_date, update_user, update_date
        FROM pcc_email_push_log
        WHERE send_status = 'FAILED'
          AND retry_count &lt; max_retry
          AND (next_retry_time IS NULL OR next_retry_time &lt;= SYSDATE)
        ORDER BY create_date ASC
    </select>

    <!-- 根据条件查询邮件推送日志列表 -->
    <select id="selectByCondition" resultMap="ResultMap">
        SELECT id, batch_id, email_type, subject, content, from_email, to_email, cc_email, bcc_email,
               user_no, dept_name, business_id, business_type, send_status, send_time, retry_count,
               max_retry, next_retry_time, error_code, error_message, attachment_info,
               create_user, create_date, update_user, update_date
        FROM pcc_email_push_log
        WHERE 1=1
        <if test="emailType != null and emailType != ''">
            AND email_type = #{emailType}
        </if>
        <if test="sendStatus != null and sendStatus != ''">
            AND send_status = #{sendStatus}
        </if>
        <if test="userNo != null and userNo != ''">
            AND user_no = #{userNo}
        </if>
        <if test="businessId != null and businessId != ''">
            AND business_id LIKE '%' || #{businessId} || '%'
        </if>
        <if test="startDate != null">
            AND create_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND create_date &lt; #{endDate} + 1
        </if>
        ORDER BY create_date DESC
    </select>

    <!-- 统计邮件发送情况 -->
    <select id="selectSendStatistics" resultMap="ResultMap">
        SELECT email_type, send_status, COUNT(*) as retry_count
        FROM pcc_email_push_log
        WHERE 1=1
        <if test="emailType != null and emailType != ''">
            AND email_type = #{emailType}
        </if>
        <if test="startDate != null">
            AND create_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND create_date &lt; #{endDate} + 1
        </if>
        GROUP BY email_type, send_status
        ORDER BY email_type, send_status
    </select>

    <!-- 删除指定日期之前的日志 -->
    <delete id="deleteBeforeDate">
        DELETE FROM pcc_email_push_log WHERE create_date &lt; #{beforeDate}
    </delete>

    <!-- 根据条件统计日志数量 -->
    <select id="countByCondition" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM pcc_email_push_log
        WHERE 1=1
        <if test="emailType != null and emailType != ''">
            AND email_type = #{emailType}
        </if>
        <if test="sendStatus != null and sendStatus != ''">
            AND send_status = #{sendStatus}
        </if>
        <if test="userNo != null and userNo != ''">
            AND user_no = #{userNo}
        </if>
        <if test="businessId != null and businessId != ''">
            AND business_id LIKE '%' || #{businessId} || '%'
        </if>
        <if test="startDate != null">
            AND create_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND create_date &lt; #{endDate} + 1
        </if>
    </select>

</mapper>
