server:
  port: 8691
  servlet.context-path: /pcc

file:
  prefix: http://***************:8691/pcc

spring:
  thymeleaf:
    prefix: classpath:/templates/
    suffix: .html
    mode: HTML
    encoding: UTF-8
    cache: true # 开发时建议设置为false，生产环境设置为true
  servlet:
    multipart:
      max-file-size: 180MB
      max-request-size: 180MB
  web:
    resources:
      static-locations: classpath:/META-INF/resources/, classpath:/resources/, classpath:/static/, classpath:/public/, classpath:/templates/, file:/D:/PCC APP/PCC SERVICES/apk, file:/D:/PCCAPP/PCCSERVICES/file
  datasource:
    dynamic:
      primary: master  # 设置默认的数据源或者数据源组，默认值即为 master
      strict: false  # 严格匹配数据源，默认 false。true：未匹配到指定数据源时抛异常，false：使用默认数据源
      datasource:
        master:
          url: jdbc:oracle:thin:@**************:1521:NXERPDB
          username: DEUSER
          password: ERP-2014
          driver-class-name: oracle.jdbc.OracleDriver
        app:
          url: *******************************************
          username: DGERPWEB
          password: DG2008
          driver-class-name: oracle.jdbc.OracleDriver
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
mybatis:
  configuration:
    call-setters-on-nulls: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    jdbc-type-for-null: 'null'
  mapper-locations: classpath:mapper/*.xml
logging:
  level:
    com.zqn: info
  pattern:
    dateformat: MM-dd HH:mm:ss:SSS
uploadUrl: "D:\\PCCAPP\\PCCSERVICES\\file"
versionName: "1.7.2" # 版本名称
versionDesc: "修复若干问题..." # 更新说明
#versionType: "APK" # 更新方式
versionType: "wgt" # 更新方式
versionCode: "16" # 版本号
downloadUrl: "http://***************:8691/pcc/pcc.wgt" # 下载链接,替换成实际下载链接
#downloadUrl: "http://***************:8691/pcc/pcc.apk" # 下载链接,替换成实际下载链接
isForceUpdate: "false" # 是否强制更新